import QEntityLink from '@/components/global/q-entity-link';
import QGlossaryInfo from '@/components/global/q-glossary-info';
import QModal from '@/components/global/q-modal';
import QPlainTable from '@/components/global/q-plain-table';
import { createPromiseDialog } from '@/components/promise-dialogs';
import { company } from '@/shared/services';
import { computed, defineComponent, onMounted, ref } from 'vue';

const VotingPath = defineComponent({
  name: 'VotingPath',
  props: {
    params: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props, { emit }) {
    const popRef = ref();
    const actualcontroller = ref();

    const tableHeight = ref(200);
    const name = ref('');
    const keyNo = ref('');
    const visible = ref(false);
    const params = ref({});
    const viewData = ref<Record<string, any>>({});
    const nameLabel = ref('间接持股企业名称');
    const totalPercentLabel = ref('间接持股比例');
    const pathLabel = ref('投资链');
    const isPathChainCustom = ref(false);
    const title = ref('投资链');
    const stockName = ref('');
    const pathTsId = ref('');
    const type = ref('');
    const isControl = ref(false);
    const isInvest = ref(true);
    const reverse = ref(false);
    const hideImage = ref(false);

    const getData = ({ name: showName, keyNo: showKeyNo, viewData: showViewData }) => {
      keyNo.value = showKeyNo;
      name.value = showName;
      viewData.value = showViewData;
      visible.value = true;
      nameLabel.value = showViewData.nameLabel || '间接持股企业名称';
      totalPercentLabel.value = showViewData.totalPercentLabel || '间接持股比例';
      pathLabel.value = showViewData.pathLabel || '投资链';
      isPathChainCustom.value = showViewData.isPathChainCustom || false;
      title.value = showViewData.title || '投资链';
      stockName.value = showViewData.stockName || '';
      pathTsId.value = showViewData.pathTsId || '';
      type.value = showViewData.type || '';

      if (type.value === 'control') {
        isControl.value = true;
        reverse.value = true;
        isInvest.value = false;
      } else if (type.value === 'shareholding') {
        isControl.value = false;
        reverse.value = true;
        isInvest.value = false;
      }

      // setTimeout(() => {
      //   if (actualcontroller.value) {
      //     actualcontroller.value.onRefreshNew(showViewData.chartData, showViewData.source);
      //   }
      // }, 100);
    };

    

    const handleData = () => {
      let resCopy = { Result: res.result }
          let dataX = res.actual || res.yisiActual || []
          let data = _.cloneDeep(dataX)
          let data0 = data[0]
          if (data0) {
            if (!data0.realNames) {
              data0.ControlPercent = data0.Names?.ControlPercent
              data0.realNames = data0?.Names?.PersonList
              data0.mutiActual = (data0.Names.PersonList.length > 1)
            } else {
              if (data0.Names && data0.Names.PersonList && data0.Names.PersonList.length > 0) {
                data0.ControlPercent = data0.Names.ControlPercent
                if (!data0.realNames) {
                  data0.realNames = data0.Names.PersonList
                  data0.mutiActual = (data0.Names.PersonList.length > 1)
                }
              }
            }
            if (data0.realNames && data0.realNames.length > 0) {
              if (!data0.Name) {
                data0.Name = data0.realNames[0].Name
              }
              if (!data0.KeyNo) {
                data0.KeyNo = data0.realNames[0].KeyNo
              }
            }
            data0.showControllChart = true
            data0.source = 'ubo_id'
            data0.chartData = handleDetailWithListed(resCopy, currentKeyNo || keyNo, name)
          }
    }

    const fetchData = async () => {
      if (!props.params.keyNo) return;
      const res = await company.getVotingPath(props.params.keyNo);
      return res;
    };

    const handleCancel = () => {
      emit('resolve', null);
    };

    onMounted(async () => {
      await fetchData();
      getData({ name: name.value, keyNo: keyNo.value, viewData: viewData.value });
    });

    return {
      keyNo,
      name,
      nameLabel,
      type,
      viewData,
      handleCancel,
      totalPercentLabel,
      isPathChainCustom,
      pathLabel,
      pathTsId,
      isControl,
      reverse,
      isInvest,
    };
  },
  render() {
    return (
      <QModal visible={true} title="控制链" footer={false} onCancel={this.handleCancel}>
        <QPlainTable>
          {this.viewData.stockName && (
            <tr>
              <td class="tb" width="20%">
                股东名称
              </td>
              <td colspan="5">
                <QEntityLink coyObj={{ KeyNo: this.keyNo, Name: this.name }} />
              </td>
            </tr>
          )}
          <tr>
            <td class="tb" width="20%">
              {this.nameLabel}
            </td>
            <td colspan="5">
              {this.type === 'control' && this.viewData.mutiActual ? (
                <QEntityLink coyArr={this.viewData.Name} />
              ) : (
                <QEntityLink coyObj={{ KeyNo: this.viewData.KeyNo, Name: this.viewData.Name }} />
              )}
            </td>
          </tr>
          {this.type !== 'control' && (
            <tr>
              <td class="tb" width="20%">
                {this.viewData.PercentTotalLabel || '总持股比例'}
              </td>
              <td width="20%">{this.viewData.PercentTotal || '-'}</td>
              <td class="tb" width="15%">
                {this.viewData.StockPercentLabel || '直接持股比例'}
              </td>
              <td width="15%">{this.viewData.StockPercent || '-'}</td>
              <td class="tb" width="15%">
                {this.viewData.IndirectStockPercentLabel || '间接持股比例'}
              </td>
              <td width="15%">{this.viewData.IndirectStockPercent || '-'}</td>
            </tr>
          )}
          {this.type === 'control' && this.viewData.PercentTotal && (
            <tr>
              <td class="tb" width="20%">
                {this.totalPercentLabel}
              </td>
              <td colspan="3">{this.viewData.PercentTotal || '-'}</td>
            </tr>
          )}
          {this.isPathChainCustom && (
            <tr>
              <td class="tb" width="20%">
                直接持股
              </td>
              <td colspan="5">{this.viewData.DirectPercent || '-'}</td>
            </tr>
          )}
          {this.viewData.showControllChart && (
            <tr>
              <td class="tb" width="20%">
                控制图谱
              </td>
              <td colspan="5" style={{ padding: '0px' }}>
                <div style={{ height: '450px' }}>
                  {/* <actual-controller-charts
                    ref="actualcontroller"
                    isOutData
                    noDataStyle
                    needFuns
                    aKeyNo={this.keyNo}
                    iframe
                    aName={this.name}
                    typeMini
                    lazy-init
                  /> */}
                </div>
              </td>
            </tr>
          )}
          <tr>
            <td class="tb" width="20%">
              {this.pathLabel}
              {this.pathTsId && <QGlossaryInfo info-id={this.pathTsId} />}
            </td>
            <td colspan="5">
              {/* <app-tdpath
                name={this.name}
                key-no={this.keyNo}
                is-control={this.isControl}
                reverse={this.reverse}
                is-invest={this.isInvest}
                paths={this.viewData.Paths}
                list={this.viewData.pathList}
              /> */}
            </td>
          </tr>
        </QPlainTable>
      </QModal>
    );
  },
});

export default VotingPath;

export const openVotingPath = createPromiseDialog(VotingPath);
