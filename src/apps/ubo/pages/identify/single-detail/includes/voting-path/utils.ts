import { uuid } from '@/utils/uuid';
import _ from 'lodash';

const settings = {
  title: '实际控制人',
  defaultScale: 1,
  scaleRate: 0.2,
  scaleRange: [0.2, 4],
  duration: 300,
  nameRule: [12, 24],
  lineHeight: 1.9,
  // 节点间距
  xGap: 100,
  yGap: 80,
  // 图谱理底部距离
  bottom: 150,
  // 企业节点宽度
  enodeWidth: 182,
  // 企业节点高度
  enodeHeight: 54,
  // 人物节点宽度
  pnodeWidth: 52,
  // 人物节点高度
  pnodeHeight: 52,
  // 标记高度
  listTagHeight: 15,
  // 标签宽度
  tagWidth: 124,
  // 根节点高度
  rootnodeHeight: 36,
  // 路径限制条数
  pathLimit: 20,
  publicHeight: 30,
  fontSize: 14,
  nodeStyle: {
    backgroundFit: 'cover',
    lineHeight: 1.6,
    overlayColor: '#ffffff',
    textBgc: '#000000',
  },
  labelSize: {
    nodeLabel: 14,
    extraLabel: 12,
    linkLabel: 12,
    tagLabel: 10,
  },
  linkStyle: {
    width: 0.6,
    overlayColor: '#ffffff',
    curveStyle: 'bezier',
    arrowShape: 'triangle',
    color: '#999999',
    textColor: '#128bed',
  },
};

let xsortCount = 1
let maxlevel = 1
let hasGp = false
let hasSpecial = false

class Adjust {
  constructor (nodes, links, maxlevel, xsortCount) {
    this.nodes = nodes
    this.links = links
    this.maxlevel = maxlevel
    this.xsortCount = xsortCount
  }

  adjust = () => {
    let matrix: any = []
    this.nodes.forEach(node => {
      if (node.kzr) {
        node.level = this.maxlevel
      }
      if (!matrix[node.level]) {
        matrix[node.level] = []
      }
      matrix[node.level].push(node)
    })

    // level 整个一层都没节点，后面层往前移动
    let levelEmpty = 0
    for (const m of matrix) {
      if (!m || (m && m.length === 0)) {
        levelEmpty++
      } else if (levelEmpty) {
        for (const l of m) {
          l.level -= levelEmpty
        }
      }
    }

    // 去除空元素
    matrix = matrix.filter(d => d)

    // 路径只有1条的时候居中
    for (const m of matrix) {
      if (m.length === 1) {
        m[0].xsort = 0
      }
    }

    // xsort 调整
    for (let i = matrix.length - 1; i > 0; i--) {
      for (const v of matrix[i]) {
        const upLevelNodes = this.upLevelNodes(v)
        const avgSort = this.getAvgSort(upLevelNodes)
        if (avgSort > 0) {
          v.xsort = avgSort
        }
      }
      matrix[i].sort((a, b) => {
        return a.xsort - b.xsort
      })
    }

    // 避免节点重复
    for (let i = 1; i < matrix.length; i++) {
      for (let j = 1; j < matrix[i].length; j++) {
        const v = matrix[i][j]
        const prev = matrix[i][j - 1]
        // 与上个节点xsort相差1或0就重合
        if (v.xsort - prev.xsort <= 1) {
          // 上上节点的xsort
          let pre2vSort = 0
          if (j >= 2) {
            pre2vSort = matrix[i][j - 2].xsort
          }
          if (prev.xsort > 1 && (!pre2vSort || prev.xsort - pre2vSort > 2)) {
            v.xsort = prev.xsort + 1
            prev.xsort--
            if (this.isNodeLinked(v, prev)) {
              v.xsort++
            }
          } else {
            v.xsort = prev.xsort + 2
          }
        }
      }
    }

    // 只有两排的时候优化
    let xCount = 0
    for (let i = 1; i < matrix.length; i++) {
      if (matrix[i].length > xCount) {
        xCount = matrix[i].length
      }
    }
    if (xCount === 2 && matrix.length > 3 && matrix.length < 5 && !this.isNodeLinked(matrix[0][0], matrix[matrix.length - 1][0])) {
      for (let i = 1; i < matrix.length; i++) {
        if ((i + 1) < matrix.length && !this.isNodeLinked(matrix[i - 1][0], matrix[i + 1][0])) {
          matrix[i][0].xsort = 0
          if (matrix[i][1]) {
            matrix[i][1].xsort = 5 // （临时）
          }
        }
      }
    }

    // 线重合处理
    const topNode = matrix[matrix.length - 1][0]
    const topTargetNodes = this.targetNodes(topNode)
    for (let i = 0; i < topTargetNodes.length - 1; i++) {
      if (topTargetNodes[i].xsort === topNode.xsort && topTargetNodes[i + 1].xsort === topNode.xsort) {
        if (topTargetNodes[i + 1] === matrix[0][0]) {
          topTargetNodes[i].xsort -= 1
        } else {
          topTargetNodes[i + 1].xsort += 2
        }
      }
    }

    // 折线处理
    if (matrix.length === 3) {
      matrix[1].forEach(node => {
        this.links.forEach(link => {
          if (link.target === node.uuid && node.xsort !== 0) {
            link.taxiUp = true
          }
          if (link.source === node.uuid && node.xsort !== 0) {
            link.taxiDown = true
          }
        })
      })
    }

    // 附加折线（临时），之后通过重新计算xsort处理
    if (xCount === 2 && matrix.length === 4) {
      matrix[1].forEach(node => {
        this.links.forEach(link => {
          if (link.source === node.uuid && node.xsort !== 0) {
            link.taxiDown = true
          }
        })
      })
    }
  }

  // 获取上层链接的节点
  upLevelNodes = node => {
    const ns = []
    this.links.forEach(link => {
      if (link.target === node.uuid) {
        const targetNode = this.nodes.find(n => {
          return n.uuid === link.source
        })
        if (targetNode.level > node.level) {
          ns.push(targetNode)
        }
      }
    })
    return ns
  }

  // 获取下层链接的节点
  downLevelNodes = node => {
    const ns = []
    this.links.forEach(link => {
      if (link.source === node.uuid) {
        const sourceNode = this.nodes.find(n => {
          return n.uuid === link.target
        })
        if (sourceNode.level && sourceNode.level < node.level) {
          ns.push(sourceNode)
        }
      }
    })
    return ns
  }

  // 获取下层链接的节点
  targetNodes = node => {
    const ns = []
    this.links.forEach(link => {
      if (link.source === node.uuid) {
        const sourceNode = this.nodes.find(n => {
          return n.uuid === link.target
        })
        ns.push(sourceNode)
      }
    })
    return ns
  }

  getAvgSort = nodes => {
    if (nodes.length <= 1) {
      return 0
    }
    let xsortTotal = 0
    nodes.forEach(node => {
      xsortTotal += node.xsort
    })
    return Math.floor(xsortTotal / nodes.length)
  }

  rightMoveSort = (nodes, benginIndex, offSort) => {
    for (let i = 0; i < nodes.length; i++) {
      if (i > benginIndex) {
        nodes[i].xsort += offSort
      }
    }
  }

  isNodeLinked = (node1, node2) => {
    let isLinked = false
    this.links.forEach(link => {
      if ((link.source === node1.uuid && link.target === node2.uuid) || (link.target === node1.uuid && link.source === node2.uuid)) {
        isLinked = true
      }
    })
    return isLinked
  }
}

const findTargetNode = ({ nodes, identity }) => {
  return _.find(nodes, (nNode) => nNode.keyNo + nNode.name === identity);
};

const generateNodeAndLink = ({ nodes, nodeData, links, nextNodeData = null, isMain = null }: {
  nodes?: any, nodeData?: any, links?: any, nextNodeData?:any, isMain?: any
}) => {
  const sNode: Record<string, any> = {
    nodeId: uuid(),
    uuid: nodeData.KeyNo + nodeData.Name,
    keyNo: nodeData.KeyNo,
    name: nodeData.Name,
    org: nodeData.Org,
    image: nodeData.KeyNo[0] === 'p' ? nodeData.ImageUrl || '' : '',
    dataType: nodeData.DataType,
    tags: [],
    xsort: null,
    kzr: false,
    rsTags: [] as any[],
    level: null,
    rsStyle: null,
  };
  if (nodeData.listedAc) {
    sNode.listedAc = nodeData.listedAc as any;
  }
  if (!nextNodeData) {
    let rsTagMaxLength = 0;
    if (nodeData.IsActual) {
      rsTagMaxLength = 5;
      sNode.rsTags.push('实际控制人');
      sNode.kzr = true;
    }
    if (Number((nodeData.ControlPercent || '').split('%')[0])) {
      const str = `表决权：${nodeData.ControlPercent}`;
      sNode.rsTags.push(str);
      rsTagMaxLength = str.length;
    }
    if (Number((nodeData.PercentTotal || '').split('%')[0]) && !hasSpecial) {
      const str = `总持股比例：${nodeData.PercentTotal}`;
      sNode.rsTags.push(str);
      rsTagMaxLength = str.length;
    }
    if (sNode.rsTags.length) {
      const rsSettings = [
        {
          height: 30,
          bg: '#128bed',
          offsetY: 5,
          textOffset: 18,
        },
        {
          height: 46,
          bg: '#FF3A3A',
          offsetY: -5,
          textOffset: 36,
        },
        {
          height: 64,
          bg: '#FF3A3A',
          offsetY: -10,
          textOffset: 52,
        },
      ];
      const rsStyle = {
        ...rsSettings[sNode.rsTags.length - 1],
        color: '#ffffff',
        width: _.max([settings.tagWidth, rsTagMaxLength * 12 + 10]),
      };
      sNode.rsStyle = rsStyle;
    }
    // 兼容多个实际控制人合并的情况
    if (nodeData.HtmlLabel) {
      sNode.htmlLabel = nodeData.HtmlLabel;
    }
  }

  // 处理标签
  if (nodeData.Tags?.length) {
    _.forEach(nodeData.Tags, (tag) => {
      if (Number(tag.Type) === 402) {
        sNode.tags.push({
          type: 'topRight',
          name: tag.Name,
          color: '#128bed',
          bg: '#E2F1FC',
        });
      }
    });
  }

  if (nextNodeData) {
    maxlevel = _.max([maxlevel, Number(nodeData.Level)])!;

    // const linkStyle = kzrHelper.handleLinkForKzr(nodeData);
    const linkStyle: Record<string, any> = {}
    if (nodeData.isFromList) {
      // 来自上市公示-兼容虚线
      linkStyle.lineType = 'dashed';
    }
    /**  实控人下级虚线（指向上市公司）并识别为特殊路径 */
    if (nodeData.isMergeDashed) {
      linkStyle.lineType = 'dashed';
      linkStyle.isSpecial = true;
    }
    if (linkStyle.lineText) {
      linkStyle.lineTextWidth = measure.getTotal(linkStyle.lineText, settings.labelSize.linkLabel, true).width;
    }
    if (linkStyle.isSpecial) {
      hasSpecial = true;
    }
    const link = {
      linkId: uuid(),
      source: nodeData.KeyNo + nodeData.Name,
      target: (nextNodeData.KeyNo || nextNodeData.keyNo) + (nextNodeData.Name || nextNodeData.name),
      name: nodeData.Percent,
      // 实际控制人的前mainCount条路径中的level=1的关系 为 表决权路径 ----> 红色
      isRed: Number(nodeData.Level) === 1 && Number(isMain) === 1,
      ...linkStyle,
    };
    if (Number(nodeData.DataType) === 5) {
      const tmpPercent = Number((nodeData.Percent || '').split('%')[0]);
      link.name = '执行事务合伙人 ' + (tmpPercent ? nodeData.Percent : '');
      hasGp = true;
    } else if (nodeData.dataType === 6) {
      link.name = '法定代表人';
    }

    const targetExitLink = _.find(links, (lLink) => lLink.source === link.source && lLink.target === link.target);
    if (!targetExitLink) {
      links.push(link);
    } else {
      targetExitLink.isRed = targetExitLink.isRed || link.isRed;
    }
  }
  let targetNode = _.find(nodes, (nNode) => nNode.keyNo === sNode.keyNo && nNode.name === sNode.name);
  if (!targetNode) {
    sNode.xsort = xsortCount;
    sNode.level = Number(nodeData.Level);
    nodeData.data = sNode;
    nodes.push(sNode);
  } else {
    sNode.level = Number(nodeData.Level);
    targetNode = _.merge(targetNode, sNode);
  }
};

const generateNodeSettings = ({ nodes }) => {
  _.forEach(nodes, (sNode) => {
    // 处理节点大小
    const style: Record<string, any> = {};
    if (sNode.level === 0) {
      style.height = settings.rootnodeHeight;
      style.width = measure.getTotal(sNode.name, settings.fontSize, true).width + 8;
      style.boc = style.bgc = '#128bed';
      style.color = '#ffffff';
    } else {
      style.width = _.startsWith(sNode.keyNo, 'p') ? settings.pnodeWidth : settings.enodeWidth;
      style.height = _.startsWith(sNode.keyNo, 'p') ? settings.pnodeHeight : settings.enodeHeight;

      if (sNode.public) {
        style.height += settings.publicHeight;
      }
      style.bgc = _.startsWith(sNode.keyNo, 'p') ? '#FF8900' : '#ffffff';
      style.boc = _.startsWith(sNode.keyNo, 'p') ? '#FF8900' : '#128bed';
      style.color = _.startsWith(sNode.keyNo, 'p') ? '#ffffff' : '#333333';
      style.bgcHover = _.startsWith(sNode.keyNo, 'p') ? '#D97716' : '';
    }

    // 名称
    const formatedName: string[] = [];
    if (sNode.level === 0) {
      formatedName.push(sNode.name);
    } else {
      const reg = new RegExp('[\u4E00-\u9FA5]+');
      let nameRule = settings.nameRule;
      if (!reg.test(sNode.name)) {
        // 非中文
        nameRule = _.startsWith(sNode.keyNo, 'p') ? [6, 10] : [22, 44];
      }
      const nameLimit = nameRule[nameRule.length - 1];
      let start = 0;
      _.forEach(nameRule, (limit, index) => {
        let tempString = (sNode.name || '').slice(start, limit);
        if (index === nameRule.length - 1 && sNode.name?.length > nameLimit) {
          tempString += '...';
        }
        if (tempString) {
          formatedName.push(tempString);
        }
        start += limit;
      });
    }
    style.label = formatedName;
    style.type = _.startsWith(sNode.keyNo, 'p') ? 'ellipse' : 'square';
    style.overlayColor = '#333333';

    style.backgroundFit = 'cover';
    style.bgi = sNode.image || 'none';

    const isMacOS = false
    // 处理textMarginY
    if (sNode.level === 0) {
      style.textMarginY = !isMacOS ? 24 : 22;
    } else {
      let sOffset = !isMacOS ? 15 : 14;
      if (style.label.length === 1) {
        sOffset = sNode.image ? 20 : 4;
      }
      style.textMarginY = style.height / 2 + sOffset;
    }

    sNode.style = style;
  });
};

const generateLinkSettings = ({ nodes, links }) => {
  _.forEach(links, (link) => {
    // 处理节点大小
    const style: Record<string, any> = {};
    const sourceNode = findTargetNode({ nodes, identity: link.source });
    const targetNode = findTargetNode({ nodes, identity: link.target });
    link.sourceNode = sourceNode;
    link.targetNode = targetNode;
    if (link.taxiUp || link.taxiDown) {
      style.curveStyle = 'taxi';
      style.taxiDirection = link.taxiUp ? 'horizontal' : 'vertical';
    }
    style.label = link.lineText;
    style.color = link.isRed ? '#FF3A3A' : '#128bed';
    style.lineColor = link.isRed ? '#FF3A3A' : '#999999';
    style.lineType = link.lineType;
    style.lineTextWidth = link.lineTextWidth;
    style.tipOffsetX = 2;
    link.lineTipsStyle = {
      width: 12,
      height: 12,
      type: 'ellipse',
      image: 'https://qcc-static.qcc.com/resources/web/omaterial/kzr-tips.png',
    };
    link.style = style;
  });
};


export const formatActualController = (data) => {
  data.Paths = _.isArray(data.ControllerData) ? data.ControllerData : [data.ControllerData];
  const nodes: any[] = [];
  const links: any[] = [];
  xsortCount = 1;
  maxlevel = 1;
  hasGp = false;
  hasSpecial = false;

  const rootNode = {
    nodeId: uuid(),
    keyNo: data.KeyNo,
    name: data.CompanyName,
    uuid: data.KeyNo + data.CompanyName,
    isRoot: true,
    level: 0,
  };
  nodes.push(rootNode);

  _.forEach(data.Paths, (kzrNode) => {
    const limitPathList = _.clone(kzrNode.Paths || []).slice(0, settings.pathLimit);
    _.forEach(limitPathList, (singlePath) => {
      const pathResverse = singlePath.reverse();
      const isMain = pathResverse[0].IsMain;
      for (let i = pathResverse.length - 1; i >= 0; i--) {
        const pathNode = pathResverse[i];
        const pathNodeNext = i === pathResverse.length - 1 ? rootNode : pathResverse[i + 1];
        if (i === 0) {
          pathNode.Name = kzrNode.Name;
          /** 实控人合并场景下指向上市公司虚线的特殊处理（只针对第一条路径） */
          pathNode.isMergeDashed = Number(pathNodeNext?.IsPublic) === 1;
        }
        generateNodeAndLink({ nodes, links, nodeData: pathNode, nextNodeData: pathNodeNext, isMain });
      }
    });
    xsortCount += 2;
    generateNodeAndLink({ nodes, links, nodeData: kzrNode });
  });

  new Adjust(nodes, links, maxlevel, xsortCount).adjust();

  // 处理样式
  generateNodeSettings({ nodes });
  generateLinkSettings({ nodes, links });
  return {
    name: data.CompanyName,
    keyNo: data.KeyNo,
    nodes,
    links,
    paths: data.Paths,
    controllerData: data.ControllerData,
    actualControl: data.FinalActualControl || data.ActualControl,
    mergedActualControl: data.MergedActualControl,
    hasFinalAc: !!data.FinalActualControl,
    acListForTips: _.map(_.get(data, 'ActualControl.PersonList', []), (o) => {
      return {
        keyNo: o.KeyNo,
        name: o.Name,
        org: o.Org,
      };
    }),
    hasGp,
  };
};

export const handleDetailWithListed = (parseData, eid, ename) => {
  const data: Record<string, any> = parseData?.Result || {};
  data.KeyNo = data._id || eid;
  if (!data.CompanyName) {
    data.CompanyName = ename;
  }
  const ac: any[] = [];
  if (data.Names && data.Names.length) {
    data.Names.forEach((item) => {
      // 实际控制人
      if (Number(item.IsActual)) {
        ac.push(item);
      }
    });
  }
  /** 无实际控制人 但是上市公司公示情况 */
  const listedAc = _.get(data, 'FinalActualControl.PersonList', null) || _.get(data, 'ActualControl.PersonList', []);
  const listedPercent = _.get(data, 'ActualControl.ControlPercent', '');
  /** 上市公示超过1个 或者 qcc大数据计算无实际控制人 但是 有上市公示 情况下 */
  if (listedAc.length > 0) {
    const listedName: string[] = [];
    const htmlLabel: string[] = [];
    _.forEach(listedAc, (listedAcItem) => {
      listedName.push(listedAcItem.Name);
      htmlLabel.push(`<span>${listedAcItem.Name}</span>`);
      // htmlLabel.push(
      //   listedAcItem.KeyNo
      //     ? $util.getCompanyOrPersonLinkerByOrg(listedAcItem.Name, listedAcItem.KeyNo)
      //     : `<span>${listedAcItem.Name}</span>`
      // );
    });
    const formatedHtmlLable =
      '<div style="font-size: 12px;max-width: 182px; max-height: 34px; padding: 0px 12px; line-height: 18px; overflow: hidden; text-overflow: ellipsis; -webkit-line-clamp: 2;display: -webkit-box; -webkit-box-orient: vertical; text-align:left;">' +
      htmlLabel.join('、') +
      '</div>';
    data.ControllerData = {
      KeyNo: listedAc.length === 1 ? listedAc[0].KeyNo : '',
      Name: listedName.join('、'),
      HtmlLabel: listedAc.length > 1 ? formatedHtmlLable : '',
      listedAc,
      MainCount: 1,
      IsActual: 1,
      Percent: listedPercent + (listedPercent ? '%(表决权)' : ''),
      PercentTotal: '',
      Level: '1',
      Paths: [
        [
          {
            Name: listedName.join('、'),
            listedAc,
            HtmlLabel: listedAc.length > 1 ? formatedHtmlLable : '',
            KeyNo: listedAc.length === 1 ? listedAc[0].KeyNo : '',
            isFromList: true,
            Percent: listedPercent + (listedPercent ? '%(表决权)' : ''),
            IsMain: '1',
            Level: '1',
          },
        ],
      ],
    };
  } else {
    if (ac?.length === 1) {
      const acItem: Record<string, any> = _.head(ac) || {};
      const personObj = acItem.Names || {};
      const personList = personObj?.PersonList || [];
      const controlPercent = personObj?.ControlPercent || '';
      const isSinglePerson = personList.length === 1;
      if (isSinglePerson) {
        /** 单实控人 */
        const personParams: Record<string, any> = _.head(personList) || {};
        data.ControllerData = {
          KeyNo: personParams.KeyNo,
          Name: personParams.Name,
          Percent: personParams.Percent,
          PercentTotal: personParams.PercentTotal,
          Level: '1',
          PathCount: acItem.PathCount,
          Paths: acItem.Paths,
          MainCount: acItem.MainCount,
          OperType: acItem.OperType,
          ControlPercent: controlPercent,
          IsActual: acItem.IsActual,
          ActualType: acItem.ActualType,
          Tags: acItem.Tags,
          ImageUrl: personParams.ImageUrl,
          CollapsedPaths: acItem.CollapsedPaths,
          CoyCountOfSameAC: personParams.CoyCountOfSameAC,
          Job: acItem.Job,
        };
      } else {
        /** 多实控人顶点合并 */
        const combineList: string[] = [];
        const combineLabel: string[] = [];
        _.forEach(personList, (p) => {
          combineList.push(p.Name);
          // combineLabel.push(p.KeyNo ? $util.getCompanyOrPersonLinkerByOrg(p.Name, p.KeyNo) : `<span>${p.Name}</span>`);
          combineLabel.push(`<span>${p.Name}</span>`);
        });
        const formatedHtmlLable =
          '<div style="font-size: 12px;max-width: 182px; max-height: 34px; padding: 0px 12px; line-height: 18px; overflow: hidden; text-overflow: ellipsis; -webkit-line-clamp: 2;display: -webkit-box; -webkit-box-orient: vertical; text-align:left;">' +
          _.join(combineLabel, '、') +
          '</div>';
        data.ControllerData = {
          KeyNo: '',
          Name: _.join(combineList, '、'),
          HtmlLabel: formatedHtmlLable,
          listedAc: personList,
          MainCount: 1,
          IsActual: 1,
          Percent: controlPercent + (controlPercent ? '%(表决权)' : ''),
          PercentTotal: '',
          Level: '1',
          Paths: acItem.Paths,
          ControlPercent: controlPercent,
        };
        data.MergedActualControl = {
          PersonList: personList,
          ControlPercent: controlPercent,
        };
      }
    } else {
      /** 多实控人顶点不合并 */
      data.ControllerData = _.map(ac, (acItem: Record<string, any>) => {
        const personObj = acItem.Names || {};
        const personParams: Record<string, any> = _.head(personObj?.PersonList) || {};
        return {
          KeyNo: personParams.KeyNo,
          Name: personParams.Name,
          Percent: personParams.Percent,
          PercentTotal: personParams.PercentTotal,
          Level: '1',
          PathCount: acItem.PathCount,
          Paths: acItem.Paths,
          MainCount: acItem.MainCount,
          OperType: acItem.OperType,
          IsActual: acItem.IsActual,
          ActualType: acItem.ActualType,
          Tags: acItem.Tags,
          ImageUrl: personParams.ImageUrl,
          CollapsedPaths: acItem.CollapsedPaths,
          CoyCountOfSameAC: personParams.CoyCountOfSameAC,
          Job: acItem.Job,
        };
      });
    }
  }
  const detail: Record<string, any> = formatActualController(data);
  detail.isListed = !!data.IsListed || false;
  /** 判断是否展示比例 */
  const checkShowPercent = (percent) => percent && Number(_.split(percent, '%')[0]);
  if (_.isArray(detail.controllerData)) {
    _.forEach(detail.controllerData, (c) => {
      if (checkShowPercent(c.ControlPercent)) {
        c.showControlPercent = true;
      }
    });
  } else {
    if (checkShowPercent(detail.controllerData?.ControlPercent)) {
      detail.controllerData.showControlPercent = true;
    }
  }
  return detail;
};
