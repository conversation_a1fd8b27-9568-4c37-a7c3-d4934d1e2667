import { createPromiseDialog } from '@/components/promise-dialogs';
import { computed, defineComponent, onMounted, ref } from 'vue';
import actualControllerCharts from '../../../../../charts/actualcontroller-charts/index-account.vue';

const InvestDetail = defineComponent({
  name: 'AppInvestDetail',
  components: { actualControllerCharts },
  setup(props, { emit }) {
    const popRef = ref();
    const actualcontroller = ref();

    const tableHeight = ref(200);
    const name = ref('');
    const keyNo = ref('');
    const visible = ref(false);
    const params = ref({});
    const viewData = ref(null);
    const nameLabel = ref('间接持股企业名称');
    const totalPercentLabel = ref('间接持股比例');
    const pathLabel = ref('投资链');
    const isPathChainCustom = ref(false);
    const title = ref('投资链');
    const stockName = ref('');
    const pathTsId = ref('');
    const type = ref('');
    const isControl = ref(false);
    const isInvest = ref(true);
    const reverse = ref(false);
    const hideImage = ref(false);


    const getData = ({ name: showName, keyNo: showKeyNo, viewData: showViewData }) => {
      keyNo.value = showKeyNo;
      name.value = showName;
      viewData.value = showViewData;
      visible.value = true;
      nameLabel.value = showViewData.nameLabel || '间接持股企业名称';
      totalPercentLabel.value = showViewData.totalPercentLabel || '间接持股比例';
      pathLabel.value = showViewData.pathLabel || '投资链';
      isPathChainCustom.value = showViewData.isPathChainCustom || false;
      title.value = showViewData.title || '投资链';
      stockName.value = showViewData.stockName || '';
      pathTsId.value = showViewData.pathTsId || '';
      type.value = showViewData.type || '';

      if (type.value === 'control') {
        isControl.value = true;
        reverse.value = true;
        isInvest.value = false;
      } else if (type.value === 'shareholding') {
        isControl.value = false;
        reverse.value = true;
        isInvest.value = false;
      }

      setTimeout(() => {
        if (actualcontroller.value) {
          actualcontroller.value.onRefreshNew(showViewData.chartData, showViewData.source);
        }
      }, 100);
    };

    onMounted(() => {
      getData({ name: name.value, keyNo: keyNo.value, viewData: viewData.value });
    });

    return {
      popRef,
      actualcontroller,
      tableHeight,
      name,
      keyNo,
      visible,
      params,
      viewData,
      nameLabel,
      totalPercentLabel,
      pathLabel,
      isPathChainCustom,
      title,
      stockName,
      pathTsId,
      type,
      isControl,
      isInvest,
      reverse,
      hideImage,
    };
  },
  render() {
    return (
      <app-popup
        ref="popRef"
        title={this.title}
        class="app-popup invest-detail"
        disableBodyScroll={false}
        options={{ width: '960px' }}
        buttonOptions={[]}
      >
        {this.viewData && (
          <table class="ntable" key={this.keyNo}>
            {this.stockName && (
              <tr>
                <td class="tb" width="20%">股东名称</td>
                <td colspan="5">
                  <app-tdcoy
                    class="acNameWrap"
                    key-no={this.keyNo}
                    name={this.name}
                    image={this.viewData.bodyImageUrl}
                    hideImage={this.hideImage}
                  />
                </td>
              </tr>
            )}
            <tr>
              <td class="tb" width="20%">{this.nameLabel}</td>
              <td colspan="5">
                {this.type === 'control' && this.viewData.mutiActual ? (
                  <app-coy coy-arr={this.viewData.Name} />
                ) : (
                  <app-tdcoy
                    class="acNameWrap"
                    key-no={this.viewData.KeyNo}
                    name={this.viewData.Name}
                    image={this.viewData.ActualControllerImageUrl || this.viewData.ImageUrl}
                    hideImage={this.hideImage}
                  />
                )}
              </td>
            </tr>
            {this.type !== 'control' && (
              <tr>
                <td class="tb" width="20%">{this.viewData.PercentTotalLabel || "总持股比例"}</td>
                <td width="20%">{this.viewData.PercentTotal || "-"}</td>
                <td class="tb" width="15%">{this.viewData.StockPercentLabel || "直接持股比例"}</td>
                <td width="15%">{this.viewData.StockPercent || "-"}</td>
                <td class="tb" width="15%">{this.viewData.IndirectStockPercentLabel || "间接持股比例"}</td>
                <td width="15%">{this.viewData.IndirectStockPercent || "-"}</td>
              </tr>
            )}
            {this.type === 'control' && this.viewData.PercentTotal && (
              <tr>
                <td class="tb" width="20%">{this.totalPercentLabel}</td>
                <td colspan="3">{this.viewData.PercentTotal || "-"}</td>
              </tr>
            )}
            {this.isPathChainCustom && (
              <tr>
                <td class="tb" width="20%">直接持股</td>
                <td colspan="5">{this.viewData.DirectPercent || "-"}</td>
              </tr>
            )}
            {this.viewData.showControllChart && (
              <tr>
                <td class="tb" width="20%">控制图谱</td>
                <td colspan="5" style={{ padding: '0px' }}>
                  <div style={{ position: 'relative', height: '450px' }}>
                    <actual-controller-charts
                      ref="actualcontroller"
                      isOutData
                      noDataStyle
                      needFuns
                      aKeyNo={this.keyNo}
                      iframe
                      aName={this.name}
                      typeMini
                      lazy-init
                    />
                  </div>
                </td>
              </tr>
            )}
            <tr>
              <td class="tb" width="20%">
                {this.pathLabel}
                {this.pathTsId && (
                  <app-glossary-info
                    class="glossary-style"
                    info-id={this.pathTsId}
                    placement="bottom-start"
                  />
                )}
              </td>
              <td colspan="5">
                <app-tdpath
                  name={this.name}
                  key-no={this.keyNo}
                  is-control={this.isControl}
                  reverse={this.reverse}
                  is-invest={this.isInvest}
                  paths={this.viewData.Paths}
                  list={this.viewData.pathList}
                />
              </td>
            </tr>
          </table>
        )}
      </app-popup>
    );
  },
});

export default InvestDetail;

export const openInvestDetail = createPromiseDialog(InvestDetail);
